import { Component, Input } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { DynamicDialogConfig, DynamicDialogRef } from "primeng/dynamicdialog";
import { AlertHandlerService } from "../../../core/alerts/alert-handler.service";

@Component({
  selector: "app-cancel-confirmation",
  standalone: true,
  imports: [SharedBtnComponent],
  templateUrl: "./cancel-confirmation.component.html",
  styleUrl: "./cancel-confirmation.component.scss",
})
export class CancelConfirmationComponent {
  @Input() titleText: string = "";
  @Input() cancelDate: string = "";
  @Input() cancelDateValue: string | null = "";
  @Input() btnText: string = "";
 constructor(
    public config: DynamicDialogConfig,
    private alertHandlerService: AlertHandlerService
  ) {
    const d = this.config?.data || {};
    this.titleText = d.titleText ?? '';
    this.cancelDate = d.cancelDate ?? '';
    this.cancelDateValue = d.cancelDateValue ?? null;
    this.btnText = d.btnText ?? '';
  }

  cancel() {
    this.alertHandlerService.ref?.close({ action: "cancel" });
    if (this.alertHandlerService.ref)
      this.alertHandlerService.ref.close({ action: "cancel" });
    console.log("cancel");
  }
}
