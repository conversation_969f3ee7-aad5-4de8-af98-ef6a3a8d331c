import { Meta, StoryObj, moduleMetadata } from "@storybook/angular";
import { SubscriptionPackageComponent } from "./subscription-package.component";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { AlertHandlerService } from "@src/app/modules/core/alerts/alert-handler.service";
import { DialogService, DynamicDialogConfig } from "primeng/dynamicdialog";

const meta: Meta<SubscriptionPackageComponent> = {
  title: "BusinessPackages/SubscriptionPackage",
  component: SubscriptionPackageComponent,
  tags: ["autodocs"],
  decorators: [
    moduleMetadata({
      imports: [SharedBtnComponent, PackageDetailsComponent],
      providers: [AlertHandlerService, DialogService, DynamicDialogConfig],
      
    }),
  ],
  // render: (args: SubscriptionPackageComponent) => ({
  //   props: args,
  // }),
};

export default meta;
type Story = StoryObj<SubscriptionPackageComponent>;

export const Default: Story = {
  args: {
    titleText: "اشترك في باقة خطة المبتدئين",
    planTitle: "خطة المبتدئين (Starter Plan) ",
    price: '3,840',
  },
};

export const BusinessPro: Story = {
  args: {
    titleText: "اشترك في باقة برو للأعمال",
    planTitle: "برو للأعمال (Business Pro) ",
    price: '7,000',
  },
};
