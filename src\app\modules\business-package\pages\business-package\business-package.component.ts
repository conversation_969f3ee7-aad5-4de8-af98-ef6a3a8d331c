import { Component, inject } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackagePlanComponent } from "../../components/package-plan/package-plan.component";
import { CustomPackagePlanComponent } from "../../components/custom-package-plan/custom-package-plan.component";
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
import { CancelConfirmationComponent } from '../../components/cancel-confirmation/cancel-confirmation.component';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-business-package',
  standalone: true,
  imports: [SharedBtnComponent, PackagePlanComponent, CustomPackagePlanComponent],
  templateUrl: './business-package.component.html',
  styleUrl: './business-package.component.scss'
})
export class BusinessPackageComponent {

  private alerts = inject(AlertHandlerService);
  constructor(
    private alertHandlerService: AlertHandlerService,
    public config: DynamicDialogConfig
  ) {}
  goToSubscription() {
    this.alertHandlerService.ref?.close({ action: 'subscribe' });
    if (this.alertHandlerService.ref)
    this.alerts.DynamicDialogOpen(CancelConfirmationComponent, {
      titleText: 'هل أنت متأكد من رغبتك في تحويل الي حساب فرد؟  ',
      cancelDate: 'عند تحويل نوع الحساب ، سيتم إزالة أي إضافة او اعلانات تم شراؤها مسبقًا',
      btnText: 'تأكيد التحويل ',
    }, (res) => {
      if (res?.confirmed) {

      }
    });
  }
}
