import { Component, inject, Input } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { CommonModule } from "@angular/common";
import { ViewSwitchService } from "../../services/view-switch.service";
import { DynamicDialogConfig } from "primeng/dynamicdialog";
import { AlertHandlerService } from "../../../core/alerts/alert-handler.service";
import { SubscriptionPackageComponent } from "../subscription-package/subscription-package.component";

@Component({
  selector: "app-package-plan",
  standalone: true,
  imports: [SharedBtnComponent, PackageDetailsComponent, CommonModule],
  templateUrl: "./package-plan.component.html",
  styleUrl: "./package-plan.component.scss",
})
export class PackagePlanComponent {
  @Input() badge?: string | null = "الأكثر مبيعاً";
  @Input() planTitle?: string = "خطة المبتدئين";
  @Input() price?: string = "3840";
  @Input() currency = "جنيه";
  @Input() billingCycle = "شهريًا";
  private alerts = inject(AlertHandlerService);
  constructor(
    public alertHandlerService: AlertHandlerService,
    public config: DynamicDialogConfig
  ) {}

    goToSubscription() {
      this.alertHandlerService.ref?.close({ action: 'subscribe', planTitle: this.planTitle, price: this.price });
      if (this.alertHandlerService.ref)
      this.alerts.DynamicDialogOpen(SubscriptionPackageComponent, {
        titleText: 'اشترك في باقة خطة المبتدئين',
        planTitle: this.planTitle,
        price: this.price,
        currency: this.currency,
        billingCycle: this.billingCycle,
        duration: '30 يومًا',
        adCount: 12,
        availablePoints: 250,
        features: ['شارة مميزة', 'دعم العلامة التجارية', 'تحليلات متقدمة'],
        support: '24/7 عبر واتساب',
        renewalNote: 'يتم تجديد الباقة تلقائيًا ما لم يتم الإلغاء',
      }, (res) => {
        if (res?.confirmed) {
  
        }
      });
    }
}
