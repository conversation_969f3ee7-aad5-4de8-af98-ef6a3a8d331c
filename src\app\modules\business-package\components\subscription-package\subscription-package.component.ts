import { Component, Input } from '@angular/core';
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { PackageDetailsComponent } from "../package-details/package-details.component";
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertHandlerService } from '@src/app/modules/core/alerts/alert-handler.service';
@Component({
  selector: 'app-subscription-package',
  standalone: true,
  imports: [SharedBtnComponent, PackageDetailsComponent],
  templateUrl: './subscription-package.component.html',
  styleUrl: './subscription-package.component.scss'
})
export class SubscriptionPackageComponent {
   @Input() titleText: string = '';
   @Input() planTitle: string = '';
   @Input() price: string = '';
  constructor(
    private alertHandlerService: AlertHandlerService,
    public config: DynamicDialogConfig
  ) {}
  pay() {
    if (this.alertHandlerService.ref)
    this.alertHandlerService.ref?.close({ action: 'pay', planTitle: this.planTitle, price: this.price });
    console.log('pay');
    
  }
}
